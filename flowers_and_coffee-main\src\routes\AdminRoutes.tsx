import React from "react";
import { Routes, Route, Navigate } from "react-router-dom";
import AdminLayout from "../layouts/admin/index";
import { useAppSelector } from "../stores/hooks";

// Admin Pages
import AdminDashboard from "../pages/admin/dashboard/index";
import AdminProducts from "../pages/admin/products/index";
import AdminOrders from "../pages/admin/orders/index";
import AdminUsers from "../pages/admin/users/index";
import AdminAnalytics from "../pages/admin/analytics/index";
import AdminSettings from "../pages/admin/settings/index";

const AdminRoutes: React.FC = () => {
  const { user, isAuthenticated } = useAppSelector((state) => state.authentication);

  // Check if user is authenticated and is admin
  if (!isAuthenticated || user?.roleId !== 4) {
    return <Navigate to="/auth/login" replace />;
  }

  return (
    <AdminLayout>
      <Routes>
        <Route path="/" element={<Navigate to="/admin/dashboard" replace />} />
        <Route path="/dashboard" element={<AdminDashboard />} />
        <Route path="/products" element={<AdminProducts />} />
        <Route path="/orders" element={<AdminOrders />} />
        <Route path="/users" element={<AdminUsers />} />
        <Route path="/analytics" element={<AdminAnalytics />} />
        <Route path="/settings" element={<AdminSettings />} />
        <Route path="*" element={<Navigate to="/admin/dashboard" replace />} />
      </Routes>
    </AdminLayout>
  );
};

export default AdminRoutes;
