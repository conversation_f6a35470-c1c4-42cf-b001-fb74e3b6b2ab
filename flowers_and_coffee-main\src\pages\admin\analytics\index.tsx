import React from "react";
import {
  Box,
  Typography,
  Grid,
  Paper,
  Card,
  CardContent,
} from "@mui/material";
import {
  TrendingUp,
  TrendingDown,
  Assessment,
  Timeline,
} from "@mui/icons-material";

const AdminAnalytics: React.FC = () => {
  const analyticsData = [
    {
      title: "Doanh thu tháng này",
      value: "125,000,000đ",
      change: "+12.5%",
      trend: "up",
      icon: <TrendingUp />,
      color: "#4caf50",
    },
    {
      title: "Đơn hàng hoàn thành",
      value: "1,234",
      change: "+8.2%",
      trend: "up",
      icon: <Assessment />,
      color: "#2196f3",
    },
    {
      title: "Sản phẩm bán chạy",
      value: "89",
      change: "-2.1%",
      trend: "down",
      icon: <Timeline />,
      color: "#ff9800",
    },
    {
      title: "Kh<PERSON>ch hàng mới",
      value: "456",
      change: "+15.3%",
      trend: "up",
      icon: <TrendingUp />,
      color: "#9c27b0",
    },
  ];

  return (
    <Box>
      <Typography
        variant="h4"
        sx={{
          mb: 4,
          fontWeight: 700,
          color: "#333",
        }}
      >
        📊 Thống kê & Phân tích
      </Typography>

      <Grid container spacing={3}>
        {analyticsData.map((item, index) => (
          <Grid item xs={12} sm={6} md={3} key={index}>
            <Card
              sx={{
                borderRadius: 3,
                border: "1px solid #f0f0f0",
                transition: "all 0.3s ease",
                "&:hover": {
                  transform: "translateY(-4px)",
                  boxShadow: "0 12px 24px rgba(0,0,0,0.1)",
                },
              }}
            >
              <CardContent sx={{ p: 3 }}>
                <Box
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "space-between",
                    mb: 2,
                  }}
                >
                  <Box
                    sx={{
                      bgcolor: `${item.color}20`,
                      color: item.color,
                      p: 1,
                      borderRadius: 2,
                    }}
                  >
                    {item.icon}
                  </Box>
                  <Box
                    sx={{
                      display: "flex",
                      alignItems: "center",
                      color: item.trend === "up" ? "#4caf50" : "#f44336",
                    }}
                  >
                    {item.trend === "up" ? <TrendingUp /> : <TrendingDown />}
                    <Typography variant="body2" sx={{ ml: 0.5, fontWeight: 600 }}>
                      {item.change}
                    </Typography>
                  </Box>
                </Box>
                <Typography
                  variant="h4"
                  sx={{
                    fontWeight: 700,
                    color: "#333",
                    mb: 1,
                  }}
                >
                  {item.value}
                </Typography>
                <Typography
                  variant="body2"
                  color="text.secondary"
                  sx={{ fontWeight: 500 }}
                >
                  {item.title}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      <Grid container spacing={3} sx={{ mt: 2 }}>
        <Grid item xs={12} md={8}>
          <Paper
            sx={{
              p: 3,
              borderRadius: 3,
              border: "1px solid #f0f0f0",
            }}
          >
            <Typography
              variant="h6"
              sx={{
                mb: 3,
                fontWeight: 600,
                color: "#333",
              }}
            >
              📈 Biểu đồ doanh thu
            </Typography>
            <Box
              sx={{
                height: 300,
                display: "flex",
                alignItems: "center",
                justifyContent: "center",
                bgcolor: "#f8f9fa",
                borderRadius: 2,
                border: "2px dashed #e0e0e0",
              }}
            >
              <Typography variant="body1" color="text.secondary">
                Biểu đồ sẽ được hiển thị ở đây
              </Typography>
            </Box>
          </Paper>
        </Grid>

        <Grid item xs={12} md={4}>
          <Paper
            sx={{
              p: 3,
              borderRadius: 3,
              border: "1px solid #f0f0f0",
            }}
          >
            <Typography
              variant="h6"
              sx={{
                mb: 3,
                fontWeight: 600,
                color: "#333",
              }}
            >
              🏆 Top sản phẩm
            </Typography>
            <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
              {[
                { name: "Hoa hồng đỏ", sales: "234 đơn", color: "#f44336" },
                { name: "Cà phê đen", sales: "189 đơn", color: "#ff9800" },
                { name: "Hoa tulip", sales: "156 đơn", color: "#9c27b0" },
                { name: "Cappuccino", sales: "134 đơn", color: "#4caf50" },
              ].map((product, index) => (
                <Box
                  key={index}
                  sx={{
                    display: "flex",
                    alignItems: "center",
                    justifyContent: "space-between",
                    p: 2,
                    bgcolor: "#f8f9fa",
                    borderRadius: 2,
                  }}
                >
                  <Box sx={{ display: "flex", alignItems: "center", gap: 2 }}>
                    <Box
                      sx={{
                        width: 8,
                        height: 8,
                        borderRadius: "50%",
                        bgcolor: product.color,
                      }}
                    />
                    <Typography variant="body2" sx={{ fontWeight: 500 }}>
                      {product.name}
                    </Typography>
                  </Box>
                  <Typography
                    variant="body2"
                    sx={{ fontWeight: 600, color: "#666" }}
                  >
                    {product.sales}
                  </Typography>
                </Box>
              ))}
            </Box>
          </Paper>
        </Grid>
      </Grid>
    </Box>
  );
};

export default AdminAnalytics;
