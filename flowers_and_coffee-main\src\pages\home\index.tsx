import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardMedia,
  CardContent,
} from "@mui/material";
import bannerImage from "../../assets/banner_flower.png";
import flower1 from "../../assets/flower1.png";
import flower2 from "../../assets/flower2.png";
import flower3 from "../../assets/flower3.png";
import flower4 from "../../assets/flower4.png";
import drink1 from "../../assets/drink1.png";
import drink2 from "../../assets/drink2.png";
import drink3 from "../../assets/drink3.png";
import drink4 from "../../assets/drink4.png";
import combo1 from "../../assets/combo1.png";
import combo2 from "../../assets/combo2.png";
import combo3 from "../../assets/combo3.png";
import about1 from "../../assets/about1.jpg";
import about2 from "../../assets/about2.jpg";
import about3 from "../../assets/about3.jpg";
import about4 from "../../assets/about4.jpg";

const HomePage: React.FC = () => {
  // Image paths - replace with your actual image paths
  const flowerBouquets = [flower1, flower2, flower3, flower4];

  const drinks = [drink1, drink2, drink3, drink4];

  const combos = [
    { image: combo1, title: "Combo 1" },
    { image: combo2, title: "Combo 2" },
    { image: combo3, title: "Combo 3" },
  ];

  const storePhotos = [about1, about2, about3, about4];

  return (
    <Container maxWidth="xl" sx={{ my: 0 }}>
      {/* Hero Section */}
      <Box
        sx={{
          position: "relative",
          height: { xs: 300, md: 500 },
          mb: 6,
          width: "100%",
          overflow: "hidden",
          borderRadius: 2,
          boxShadow: "0 4px 20px rgba(0,0,0,0.1)",
        }}
      >
        <Box
          component="img"
          src={bannerImage}
          alt="White tulips garden"
          sx={{
            width: "100%",
            height: "100%",
            objectFit: "cover",
          }}
        />
        <Box
          sx={{
            position: "absolute",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            background: "linear-gradient(rgba(0,0,0,0.3), rgba(0,0,0,0.1))",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
          }}
        >
          <Box textAlign="center" color="white">
            <Typography
              variant="h2"
              component="h1"
              sx={{
                fontFamily: '"Playfair Display", serif',
                fontSize: { xs: "2.5rem", md: "3.5rem" },
                fontWeight: 700,
                textShadow: "2px 2px 4px rgba(0,0,0,0.5)",
                mb: 2,
              }}
            >
              Hope Blossoms
            </Typography>
            <Typography
              variant="h5"
              sx={{
                fontFamily: '"Dancing Script", cursive',
                fontSize: { xs: "1.5rem", md: "2rem" },
                textShadow: "1px 1px 2px rgba(0,0,0,0.5)",
              }}
            >
              Nơi hoa và cà phê gặp gỡ
            </Typography>
          </Box>
        </Box>
      </Box>

      <Container maxWidth="lg" sx={{ mt: 8 }}>
        {/* Flowers Section */}
        <Box sx={{ mb: 8 }}>
          <Typography
            variant="h4"
            component="h2"
            align="center"
            sx={{
              fontFamily: '"Playfair Display", serif',
              fontSize: { xs: "1.8rem", md: "2.5rem" },
              fontWeight: 600,
              color: "#333",
              mb: 1,
            }}
          >
            Bộ sưu tập hoa tươi mỗi ngày
          </Typography>
          <Typography
            variant="body1"
            align="center"
            sx={{
              color: "text.secondary",
              mb: 4,
              maxWidth: 600,
              mx: "auto",
            }}
          >
            Những bó hoa tươi được chọn lọc kỹ càng, mang đến vẻ đẹp tự nhiên cho không gian của bạn
          </Typography>

          <Grid container spacing={3} sx={{ mb: 5 }}>
            {flowerBouquets.map((image, index) => (
              <Grid size={{ xs: 6, md: 3 }} key={`bouquet-${index}`}>
                <Card
                  elevation={2}
                  sx={{
                    height: "100%",
                    transition: "transform 0.3s ease, box-shadow 0.3s ease",
                    "&:hover": {
                      transform: "translateY(-8px)",
                      boxShadow: "0 8px 25px rgba(0,0,0,0.15)",
                    },
                    borderRadius: 2,
                    overflow: "hidden",
                  }}
                >
                  <CardMedia
                    component="img"
                    image={image}
                    alt={`Flower bouquet ${index + 1}`}
                    sx={{
                      aspectRatio: "1/1",
                      objectFit: "cover",
                      transition: "transform 0.3s ease",
                      "&:hover": {
                        transform: "scale(1.05)",
                      },
                    }}
                  />
                </Card>
              </Grid>
            ))}
          </Grid>
        </Box>

        {/* Drinks Section */}
        <Box sx={{ mb: 8 }}>
          <Typography
            variant="h4"
            component="h2"
            align="center"
            sx={{
              fontFamily: '"Playfair Display", serif',
              fontSize: { xs: "1.8rem", md: "2.5rem" },
              fontWeight: 600,
              color: "#333",
              mb: 1,
            }}
          >
            Đồ uống đặc biệt
          </Typography>
          <Typography
            variant="body1"
            align="center"
            sx={{
              color: "text.secondary",
              mb: 4,
              maxWidth: 600,
              mx: "auto",
            }}
          >
            Thưởng thức những ly cà phê thơm ngon và đồ uống tươi mát trong không gian yên tĩnh
          </Typography>

          <Grid container spacing={3} sx={{ mb: 5 }}>
            {drinks.map((image, index) => (
              <Grid size={{ xs: 6, md: 3 }} key={`drink-${index}`}>
                <Card
                  elevation={2}
                  sx={{
                    height: "100%",
                    transition: "transform 0.3s ease, box-shadow 0.3s ease",
                    "&:hover": {
                      transform: "translateY(-8px)",
                      boxShadow: "0 8px 25px rgba(0,0,0,0.15)",
                    },
                    borderRadius: 2,
                    overflow: "hidden",
                  }}
                >
                  <CardMedia
                    component="img"
                    image={image}
                    alt={`Drink ${index + 1}`}
                    sx={{
                      aspectRatio: "1/1",
                      objectFit: "cover",
                      transition: "transform 0.3s ease",
                      "&:hover": {
                        transform: "scale(1.05)",
                      },
                    }}
                  />
                </Card>
              </Grid>
            ))}
          </Grid>
        </Box>

        {/* Combos Section */}
        <Box sx={{ mb: 8 }}>
          <Typography
            variant="h4"
            component="h2"
            align="center"
            sx={{
              fontFamily: '"Playfair Display", serif',
              fontSize: { xs: "1.8rem", md: "2.5rem" },
              fontWeight: 600,
              color: "#333",
              mb: 1,
            }}
          >
            Có thể bạn thích
          </Typography>
          <Typography
            variant="body1"
            align="center"
            sx={{
              color: "text.secondary",
              mb: 4,
              maxWidth: 600,
              mx: "auto",
            }}
          >
            Những combo đặc biệt được thiết kế riêng cho những dịp đặc biệt
          </Typography>

          <Grid container spacing={3} justifyContent="center">
            {combos.map((combo, index) => (
              <Grid size={{ xs: 12, sm: 6, md: 4 }} key={`combo-${index}`}>
                <Card
                  elevation={2}
                  sx={{
                    height: "100%",
                    transition: "transform 0.3s ease, box-shadow 0.3s ease",
                    "&:hover": {
                      transform: "translateY(-8px)",
                      boxShadow: "0 8px 25px rgba(0,0,0,0.15)",
                    },
                    borderRadius: 2,
                    overflow: "hidden",
                  }}
                >
                  <CardMedia
                    component="img"
                    image={combo.image}
                    alt={combo.title}
                    sx={{
                      aspectRatio: "1/1",
                      objectFit: "cover",
                      transition: "transform 0.3s ease",
                      "&:hover": {
                        transform: "scale(1.05)",
                      },
                    }}
                  />
                  <CardContent sx={{ p: 2, textAlign: "center" }}>
                    <Typography variant="h6" sx={{ fontWeight: 600, color: "#333" }}>
                      {combo.title}
                    </Typography>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>
        </Box>

        {/* Store Photos */}
        <Box sx={{ mb: 4 }}>
          <Typography
            variant="h4"
            component="h2"
            align="center"
            sx={{
              fontFamily: '"Playfair Display", serif',
              fontSize: { xs: "1.8rem", md: "2.5rem" },
              fontWeight: 600,
              color: "#333",
              mb: 1,
            }}
          >
            Không gian quán
          </Typography>
          <Typography
            variant="body1"
            align="center"
            sx={{
              color: "text.secondary",
              mb: 4,
              maxWidth: 600,
              mx: "auto",
            }}
          >
            Khám phá không gian ấm cúng và thư giãn tại Hope Blossoms
          </Typography>

          <Grid container spacing={3}>
            {storePhotos.map((image, index) => (
              <Grid size={{ xs: 6, md: 3 }} key={`store-${index}`}>
                <Card
                  elevation={2}
                  sx={{
                    height: "100%",
                    transition: "transform 0.3s ease, box-shadow 0.3s ease",
                    "&:hover": {
                      transform: "translateY(-8px)",
                      boxShadow: "0 8px 25px rgba(0,0,0,0.15)",
                    },
                    borderRadius: 2,
                    overflow: "hidden",
                  }}
                >
                  <CardMedia
                    component="img"
                    image={image}
                    alt={`Store interior ${index + 1}`}
                    sx={{
                      aspectRatio: "1/1",
                      objectFit: "cover",
                      transition: "transform 0.3s ease",
                      "&:hover": {
                        transform: "scale(1.05)",
                      },
                    }}
                  />
                </Card>
              </Grid>
            ))}
          </Grid>
        </Box>
      </Container>
    </Container>
  );
};

export default HomePage;
