﻿using JucieAndFlower.Data.Enities.Order;
using JucieAndFlower.Data.Models;
using JucieAndFlower.Service.Interface;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;

namespace JucieAndFlower.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class OrdersController : Controller
    {
        private readonly IOrderService _orderService;
        private readonly IVNPayService _vnpayService;
        private readonly IPaymentService _paymentService;
        public OrdersController(IOrderService orderService, IVNPayService vnpayService, IPaymentService paymentService)
        {
            _orderService = orderService;
            _vnpayService = vnpayService;
            _paymentService = paymentService;
        }


        [HttpGet]
        public async Task<IActionResult> GetOrderById(int id)
        {
            int userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? throw new Exception("User ID claim not found"));
            var order = await _orderService.GetOrderByIdAsync(id);
            if (order == null) return NotFound();
            return Ok(order);
        }

        // Redirect đến frontend với kết quả thanh toán
        [HttpGet("payment-return")]
        public async Task<IActionResult> PaymentReturn()
        {
            var response = _vnpayService.GetReturnData(Request.Query);
            Console.WriteLine("VNPay Response OrderId: " + response.OrderId);

            // Lấy thông tin đơn hàng
            var order = await _orderService.GetOrderByIdAsync(response.OrderId);
            if (order == null)
            {
                // Redirect với thông báo lỗi
                return Redirect($"http://localhost:5173/payment-result?success=false&message=Order not found");
            }

            // Tạo bản ghi thanh toán
            var payment = new Payment
            {
                OrderId = order.OrderId,
                PaymentMethod = "VNPay",
                PaidAmount = order.FinalAmount,
                PaymentDate = DateTime.Now,
                Status = response.Success ? "Paid" : "Cancel"
            };

            await _paymentService.AddPaymentAsync(payment);

            if (response.Success)
            {
                // Cập nhật trạng thái đơn hàng thành công
                await _orderService.MarkOrderAsCompleteAsync(order.OrderId);

                // Redirect đến frontend với thông tin thành công
                return Redirect($"http://localhost:5173/payment-result?success=true&orderId={order.OrderId}&amount={order.FinalAmount}");
            }
            else
            {
                // Hủy đơn hàng nếu thanh toán thất bại
                await _orderService.MarkOrderAsCanceledAsync(order.OrderId);

                // Redirect đến frontend với thông báo thất bại
                return Redirect($"http://localhost:5173/payment-result?success=false&orderId={order.OrderId}&message=Payment failed or canceled");
            }
        }


        [HttpPost("from-cart")]
        public async Task<IActionResult> CreateOrderFromCart([FromBody] OrderFromCartDTO dto)
        {
            int userId = int.Parse(User.FindFirst(ClaimTypes.NameIdentifier)?.Value ?? throw new Exception("User ID claim not found"));
            dto.UserId = userId;
            var order = await _orderService.CreateOrderFromCartAsync(dto);
            decimal totalAmount = (decimal)order.FinalAmount;
            var paymentUrl = _vnpayService.CreatePaymentUrl(order.OrderId, totalAmount, HttpContext);

            return Ok(new
            {
                order.OrderId,
                PaymentUrl = paymentUrl
            });
        }


    }
}
