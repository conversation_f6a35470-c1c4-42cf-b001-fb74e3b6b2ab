import React, { useState, useEffect } from "react";
import {
  Box,
  Typography,
  Card,
  CardMedia,
  CardContent,
  Button,
  Container,
  Pagination,
  CircularProgress,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
} from "@mui/material";
import { Add, ShoppingCart, Remove } from "@mui/icons-material";
import Grid from "@mui/material/Grid";
import { useNavigate } from "react-router-dom";
import bannerImage from "../../assets/banner_flower.png";
import "../styles/FontStyle.scss";
import { useAppDispatch, useAppSelector } from "../../stores/hooks";
import { setFlowerDetail } from "../../stores/reducers/Flower";
import { addCartItem, setCartItems } from "../../stores/reducers/Cart";
import { productsAPI, cartAPI } from "../../services/api";
import { Product } from "../../types";
import { toast } from "react-toastify";

const FlowerShopPage = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { isAuthenticated } = useAppSelector((state) => state.Authentication);

  const [products, setProducts] = useState<Product[]>([]);
  const [loading, setLoading] = useState(false);
  const [dialogOpen, setDialogOpen] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState<Product | null>(null);
  const [quantity, setQuantity] = useState(1);

  useEffect(() => {
    loadProducts();
  }, []);

  const loadProducts = async () => {
    try {
      setLoading(true);
      console.log('Loading products...');
      const data = await productsAPI.getAll();
      console.log('Products loaded:', data);

      if (Array.isArray(data)) {
        const availableProducts = data.filter((product: Product) => product.isAvailable);
        console.log('Available products:', availableProducts);
        setProducts(availableProducts);
      } else {
        console.error('Products data is not an array:', data);
        setProducts([]);
        toast.error("Dữ liệu sản phẩm không hợp lệ");
      }
    } catch (error: any) {
      console.error('Error loading products:', error);
      toast.error(`Không thể tải danh sách sản phẩm: ${error.message}`);
      setProducts([]);
    } finally {
      setLoading(false);
    }
  };

  const handleClickFlower = (product: Product) => {
    dispatch(setFlowerDetail(product));
    navigate("/flower/detail");
  };

  const handleAddToCart = (product: Product) => {
    if (!isAuthenticated) {
      toast.warning("Vui lòng đăng nhập để thêm sản phẩm vào giỏ hàng");
      navigate("/auth/login");
      return;
    }

    setSelectedProduct(product);
    setQuantity(1);
    setDialogOpen(true);
  };

  const handleConfirmAddToCart = async () => {
    if (!selectedProduct) return;

    try {
      console.log('Adding product to cart:', selectedProduct.name, 'quantity:', quantity);
      await cartAPI.addItem(selectedProduct.productId, quantity);

      // Always reload cart from server to get accurate data
      const cartData = await cartAPI.getCart();
      console.log('Reloaded cart data:', cartData);
      dispatch(setCartItems(cartData));

      toast.success(`Đã thêm ${selectedProduct.name} vào giỏ hàng`);
      setDialogOpen(false);
    } catch (error: any) {
      console.error('Error adding to cart:', error);
      toast.error(`Không thể thêm sản phẩm vào giỏ hàng: ${error.message}`);
    }
  };

  return (
    <Container maxWidth="xl" sx={{ py: 4 }}>
      <Box
        sx={{
          position: "relative",
          height: "500px",
          mb: 4,
          overflow: "hidden",
          borderRadius: 1,
        }}
      >
        <CardMedia
          component="img"
          height="500px"
          image={bannerImage}
          alt="Banner of flowers"
          sx={{
            objectFit: "cover",
            width: "100%",
          }}
        />
        <Typography
          variant="h1"
          component="h1"
          className="workshop-title"
          sx={{
            position: "absolute",
            top: "50%",
            left: "50%",
            transform: "translate(-50%, -50%)",
          }}
        >
          Hoa
        </Typography>
      </Box>
      <Container maxWidth="lg" sx={{ mt: 10 }}>
        {loading ? (
          <Box display="flex" justifyContent="center" my={4}>
            <CircularProgress />
          </Box>
        ) : (
          <Grid container spacing={4}>
            {products.map((product) => (
              <Grid size={{ xs: 6, sm: 3 }} key={product.productId}>
                <Card
                  sx={{
                    height: "100%",
                    display: "flex",
                    flexDirection: "column",
                    borderRadius: 3,
                    overflow: "hidden",
                    transition: "all 0.3s ease",
                    border: "1px solid #f0f0f0",
                    "&:hover": {
                      transform: "translateY(-8px)",
                      boxShadow: "0 12px 24px rgba(158, 101, 92, 0.15)",
                      border: "1px solid #9e655c",
                    },
                  }}
                >
                  <Box sx={{ position: "relative", overflow: "hidden" }}>
                    <CardMedia
                      component="img"
                      height="220"
                      image={product.imageUrl}
                      alt={product.name}
                      sx={{
                        objectFit: "cover",
                        transition: "transform 0.3s ease",
                        "&:hover": {
                          transform: "scale(1.05)",
                        },
                      }}
                    />
                    {/* Price Badge */}
                    <Box
                      sx={{
                        position: "absolute",
                        top: 12,
                        right: 12,
                        bgcolor: "rgba(158, 101, 92, 0.9)",
                        color: "white",
                        px: 2,
                        py: 0.5,
                        borderRadius: 2,
                        backdropFilter: "blur(4px)",
                      }}
                    >
                      <Typography
                        variant="body2"
                        sx={{ fontWeight: 600, fontSize: "0.85rem" }}
                      >
                        {product.price.toLocaleString("vi-VN")}đ
                      </Typography>
                    </Box>
                  </Box>

                  <CardContent
                    sx={{
                      p: 2,
                      pb: 1,
                      flexGrow: 1,
                      display: "flex",
                      flexDirection: "column",
                    }}
                  >
                    <Typography
                      variant="h6"
                      component="div"
                      sx={{
                        fontWeight: 600,
                        color: "#333",
                        mb: 1,
                        fontSize: "1rem",
                        lineHeight: 1.3,
                        display: "-webkit-box",
                        WebkitLineClamp: 2,
                        WebkitBoxOrient: "vertical",
                        overflow: "hidden",
                      }}
                    >
                      {product.name}
                    </Typography>

                    {/* Rating or Category could go here */}
                    <Typography
                      variant="body2"
                      color="text.secondary"
                      sx={{ mb: 2, fontSize: "0.85rem" }}
                    >
                      Hoa tươi cao cấp
                    </Typography>
                  </CardContent>

                  <Box
                    sx={{
                      p: 2,
                      pt: 0,
                      display: "flex",
                      gap: 1,
                    }}
                  >
                    <Button
                      onClick={() => handleClickFlower(product)}
                      variant="outlined"
                      size="small"
                      sx={{
                        borderColor: "#9e655c",
                        color: "#9e655c",
                        textTransform: "none",
                        borderRadius: 2,
                        py: 1,
                        flex: 1,
                        fontWeight: 500,
                        "&:hover": {
                          borderColor: "#8a5a52",
                          bgcolor: "rgba(158, 101, 92, 0.04)",
                        },
                      }}
                    >
                      Chi tiết
                    </Button>
                    <Button
                      onClick={() => handleAddToCart(product)}
                      variant="contained"
                      size="small"
                      sx={{
                        backgroundColor: "#9e655c",
                        color: "white",
                        textTransform: "none",
                        borderRadius: 2,
                        py: 1,
                        px: 2,
                        fontWeight: 500,
                        minWidth: "auto",
                        "&:hover": {
                          backgroundColor: "#8a5a52",
                          transform: "scale(1.05)",
                        },
                      }}
                    >
                      <ShoppingCart fontSize="small" />
                    </Button>
                  </Box>
                </Card>
              </Grid>
            ))}
          </Grid>
        )}
        <Box sx={{ display: "flex", justifyContent: "center", mt: 6, mb: 2 }}>
          <Pagination
            count={12}
            variant="outlined"
            shape="rounded"
            defaultPage={8}
            siblingCount={1}
            size="medium"
            sx={{
              "& .MuiPaginationItem-root": {
                borderRadius: 2,
                fontWeight: 500,
                "&.Mui-selected": {
                  bgcolor: "#9e655c",
                  color: "white",
                  "&:hover": {
                    bgcolor: "#8a5a52",
                  },
                },
                "&:hover": {
                  bgcolor: "rgba(158, 101, 92, 0.04)",
                  borderColor: "#9e655c",
                },
              },
            }}
          />
        </Box>
      </Container>

      {/* Add to Cart Dialog */}
      <Dialog
        open={dialogOpen}
        onClose={() => setDialogOpen(false)}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 3,
            overflow: 'hidden',
          }
        }}
      >
        <DialogTitle
          sx={{
            bgcolor: '#9e655c',
            color: 'white',
            textAlign: 'center',
            py: 2,
            fontSize: '1.2rem',
            fontWeight: 600
          }}
        >
          🛒 Thêm vào giỏ hàng
        </DialogTitle>
        <DialogContent sx={{ p: 3 }}>
          {selectedProduct && (
            <Box>
              {/* Product Image */}
              <Box
                sx={{
                  display: 'flex',
                  justifyContent: 'center',
                  mb: 2
                }}
              >
                <img
                  src={selectedProduct.imageUrl}
                  alt={selectedProduct.name}
                  style={{
                    width: '120px',
                    height: '120px',
                    objectFit: 'cover',
                    borderRadius: '12px',
                    border: '3px solid #f5f5f5'
                  }}
                />
              </Box>

              {/* Product Info */}
              <Typography
                variant="h6"
                gutterBottom
                sx={{
                  textAlign: 'center',
                  fontWeight: 600,
                  color: '#333',
                  mb: 1
                }}
              >
                {selectedProduct.name}
              </Typography>

              <Typography
                variant="h6"
                sx={{
                  textAlign: 'center',
                  color: '#9e655c',
                  fontWeight: 700,
                  mb: 3
                }}
              >
                {selectedProduct.price.toLocaleString("vi-VN")}đ
              </Typography>

              {/* Quantity Selector */}
              <Box sx={{ mb: 3 }}>
                <Typography
                  variant="body1"
                  sx={{
                    mb: 2,
                    fontWeight: 600,
                    color: '#333'
                  }}
                >
                  Số lượng:
                </Typography>
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    gap: 2
                  }}
                >
                  <IconButton
                    onClick={() => setQuantity(Math.max(1, quantity - 1))}
                    disabled={quantity <= 1}
                    sx={{
                      bgcolor: '#f5f5f5',
                      '&:hover': { bgcolor: '#e0e0e0' },
                      '&.Mui-disabled': { bgcolor: '#f9f9f9' }
                    }}
                  >
                    <Remove />
                  </IconButton>

                  <TextField
                    value={quantity}
                    onChange={(e) => setQuantity(Math.max(1, parseInt(e.target.value) || 1))}
                    inputProps={{
                      min: 1,
                      style: { textAlign: 'center', fontWeight: 600 }
                    }}
                    sx={{
                      width: 80,
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 2,
                        '& fieldset': {
                          borderColor: '#e0e0e0',
                        },
                        '&:hover fieldset': {
                          borderColor: '#9e655c',
                        },
                        '&.Mui-focused fieldset': {
                          borderColor: '#9e655c',
                        },
                      }
                    }}
                    type="number"
                    size="small"
                  />

                  <IconButton
                    onClick={() => setQuantity(quantity + 1)}
                    sx={{
                      bgcolor: '#f5f5f5',
                      '&:hover': { bgcolor: '#e0e0e0' }
                    }}
                  >
                    <Add />
                  </IconButton>
                </Box>
              </Box>

              {/* Total */}
              <Box
                sx={{
                  bgcolor: '#f8f9fa',
                  p: 2,
                  borderRadius: 2,
                  border: '1px solid #e9ecef'
                }}
              >
                <Box
                  sx={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center'
                  }}
                >
                  <Typography
                    variant="body1"
                    sx={{ fontWeight: 600, color: '#333' }}
                  >
                    Tổng cộng:
                  </Typography>
                  <Typography
                    variant="h6"
                    sx={{
                      fontWeight: 700,
                      color: '#9e655c'
                    }}
                  >
                    {(selectedProduct.price * quantity).toLocaleString("vi-VN")}đ
                  </Typography>
                </Box>
              </Box>
            </Box>
          )}
        </DialogContent>
        <DialogActions sx={{ p: 3, pt: 0 }}>
          <Button
            onClick={() => setDialogOpen(false)}
            variant="outlined"
            sx={{
              borderColor: '#9e655c',
              color: '#9e655c',
              borderRadius: 2,
              px: 3,
              '&:hover': {
                borderColor: '#8a5a52',
                bgcolor: 'rgba(158, 101, 92, 0.04)',
              },
            }}
          >
            Hủy
          </Button>
          <Button
            onClick={handleConfirmAddToCart}
            variant="contained"
            sx={{
              bgcolor: '#9e655c',
              borderRadius: 2,
              px: 3,
              '&:hover': {
                bgcolor: '#8a5a52',
              },
            }}
          >
            🛒 Thêm vào giỏ hàng
          </Button>
        </DialogActions>
      </Dialog>
    </Container>
  );
};

export default FlowerShopPage;
