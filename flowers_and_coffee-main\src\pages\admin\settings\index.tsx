import React, { useState } from "react";
import {
  Box,
  Typography,
  Grid,
  Paper,
  Card,
  CardContent,
  Switch,
  FormControlLabel,
  Button,
  TextField,
  Divider,
  Alert,
} from "@mui/material";
import {
  Settings,
  Notifications,
  Security,
  Palette,
  Save,
} from "@mui/icons-material";

const AdminSettings: React.FC = () => {
  const [settings, setSettings] = useState({
    emailNotifications: true,
    pushNotifications: false,
    autoBackup: true,
    maintenanceMode: false,
    darkMode: false,
    companyName: "Flowers & Coffee",
    companyEmail: "<EMAIL>",
    companyPhone: "0123456789",
  });

  const handleSwitchChange = (setting: string) => (event: React.ChangeEvent<HTMLInputElement>) => {
    setSettings(prev => ({
      ...prev,
      [setting]: event.target.checked,
    }));
  };

  const handleInputChange = (setting: string) => (event: React.ChangeEvent<HTMLInputElement>) => {
    setSettings(prev => ({
      ...prev,
      [setting]: event.target.value,
    }));
  };

  const handleSave = () => {
    // Save settings logic here
    console.log("Settings saved:", settings);
  };

  return (
    <Box>
      <Typography
        variant="h4"
        sx={{
          mb: 4,
          fontWeight: 700,
          color: "#333",
        }}
      >
        ⚙️ Cài đặt hệ thống
      </Typography>

      <Grid container spacing={3}>
        {/* Notifications Settings */}
        <Grid item xs={12} md={6}>
          <Card
            sx={{
              borderRadius: 3,
              border: "1px solid #f0f0f0",
              height: "100%",
            }}
          >
            <CardContent sx={{ p: 3 }}>
              <Box sx={{ display: "flex", alignItems: "center", mb: 3 }}>
                <Box
                  sx={{
                    bgcolor: "#2196f320",
                    color: "#2196f3",
                    p: 1,
                    borderRadius: 2,
                    mr: 2,
                  }}
                >
                  <Notifications />
                </Box>
                <Typography variant="h6" sx={{ fontWeight: 600 }}>
                  Thông báo
                </Typography>
              </Box>

              <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={settings.emailNotifications}
                      onChange={handleSwitchChange("emailNotifications")}
                      color="primary"
                    />
                  }
                  label="Thông báo qua Email"
                />
                <FormControlLabel
                  control={
                    <Switch
                      checked={settings.pushNotifications}
                      onChange={handleSwitchChange("pushNotifications")}
                      color="primary"
                    />
                  }
                  label="Thông báo đẩy"
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* System Settings */}
        <Grid item xs={12} md={6}>
          <Card
            sx={{
              borderRadius: 3,
              border: "1px solid #f0f0f0",
              height: "100%",
            }}
          >
            <CardContent sx={{ p: 3 }}>
              <Box sx={{ display: "flex", alignItems: "center", mb: 3 }}>
                <Box
                  sx={{
                    bgcolor: "#4caf5020",
                    color: "#4caf50",
                    p: 1,
                    borderRadius: 2,
                    mr: 2,
                  }}
                >
                  <Security />
                </Box>
                <Typography variant="h6" sx={{ fontWeight: 600 }}>
                  Hệ thống
                </Typography>
              </Box>

              <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={settings.autoBackup}
                      onChange={handleSwitchChange("autoBackup")}
                      color="primary"
                    />
                  }
                  label="Tự động sao lưu"
                />
                <FormControlLabel
                  control={
                    <Switch
                      checked={settings.maintenanceMode}
                      onChange={handleSwitchChange("maintenanceMode")}
                      color="warning"
                    />
                  }
                  label="Chế độ bảo trì"
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Appearance Settings */}
        <Grid item xs={12} md={6}>
          <Card
            sx={{
              borderRadius: 3,
              border: "1px solid #f0f0f0",
              height: "100%",
            }}
          >
            <CardContent sx={{ p: 3 }}>
              <Box sx={{ display: "flex", alignItems: "center", mb: 3 }}>
                <Box
                  sx={{
                    bgcolor: "#9c27b020",
                    color: "#9c27b0",
                    p: 1,
                    borderRadius: 2,
                    mr: 2,
                  }}
                >
                  <Palette />
                </Box>
                <Typography variant="h6" sx={{ fontWeight: 600 }}>
                  Giao diện
                </Typography>
              </Box>

              <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
                <FormControlLabel
                  control={
                    <Switch
                      checked={settings.darkMode}
                      onChange={handleSwitchChange("darkMode")}
                      color="primary"
                    />
                  }
                  label="Chế độ tối"
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Company Information */}
        <Grid item xs={12} md={6}>
          <Card
            sx={{
              borderRadius: 3,
              border: "1px solid #f0f0f0",
              height: "100%",
            }}
          >
            <CardContent sx={{ p: 3 }}>
              <Box sx={{ display: "flex", alignItems: "center", mb: 3 }}>
                <Box
                  sx={{
                    bgcolor: "#ff980020",
                    color: "#ff9800",
                    p: 1,
                    borderRadius: 2,
                    mr: 2,
                  }}
                >
                  <Settings />
                </Box>
                <Typography variant="h6" sx={{ fontWeight: 600 }}>
                  Thông tin công ty
                </Typography>
              </Box>

              <Box sx={{ display: "flex", flexDirection: "column", gap: 2 }}>
                <TextField
                  label="Tên công ty"
                  value={settings.companyName}
                  onChange={handleInputChange("companyName")}
                  fullWidth
                  size="small"
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      borderRadius: 2,
                    },
                  }}
                />
                <TextField
                  label="Email công ty"
                  value={settings.companyEmail}
                  onChange={handleInputChange("companyEmail")}
                  fullWidth
                  size="small"
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      borderRadius: 2,
                    },
                  }}
                />
                <TextField
                  label="Số điện thoại"
                  value={settings.companyPhone}
                  onChange={handleInputChange("companyPhone")}
                  fullWidth
                  size="small"
                  sx={{
                    "& .MuiOutlinedInput-root": {
                      borderRadius: 2,
                    },
                  }}
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      {/* Maintenance Mode Warning */}
      {settings.maintenanceMode && (
        <Alert
          severity="warning"
          sx={{
            mt: 3,
            borderRadius: 2,
          }}
        >
          ⚠️ Chế độ bảo trì đang được bật. Website sẽ không khả dụng cho người dùng.
        </Alert>
      )}

      {/* Save Button */}
      <Box sx={{ mt: 4, display: "flex", justifyContent: "center" }}>
        <Button
          variant="contained"
          size="large"
          startIcon={<Save />}
          onClick={handleSave}
          sx={{
            bgcolor: "#4caf50",
            borderRadius: 3,
            px: 4,
            py: 1.5,
            fontWeight: 600,
            "&:hover": {
              bgcolor: "#45a049",
              transform: "translateY(-2px)",
            },
            transition: "all 0.3s ease",
          }}
        >
          Lưu cài đặt
        </Button>
      </Box>
    </Box>
  );
};

export default AdminSettings;
